using System.Net.Http;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.Extensions.DependencyInjection;
using <PERSON><PERSON><PERSON>;

namespace ProScoring.Blazor.Client;

/// <summary>
/// The main entry point for the Blazor WebAssembly application.
/// </summary>
/// <param name="args">The command-line arguments.</param>
/// <returns>A task that represents the asynchronous operation.</returns>
internal static class Program
{
    static async Task Main(string[] args)
    {
        var builder = WebAssemblyHostBuilder.CreateDefault(args);

        builder.Services.AddAuthorizationCore();
        builder.Services.AddCascadingAuthenticationState();

        // Add HTTP client factory
        builder.Services.AddHttpClient(
            "DocumentationClient",
            client =>
            {
                client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress);
            }
        );

        builder.Services.AddRadzenComponents();

        await builder.Build().RunAsync();
    }
}
