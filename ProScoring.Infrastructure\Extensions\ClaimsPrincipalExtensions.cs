using System.Security.Claims;

namespace ProScoring.Infrastructure.Extensions;

/// <summary>
/// Extension methods for the ClaimsPrincipal class to enhance claim-based operations.
/// </summary>
public static class ClaimsPrincipalExtensions
{
    /// <summary>
    /// Checks if the user has a claim with the specified type and value, ignoring case sensitivity for the value.
    /// </summary>
    /// <param name="user">The ClaimsPrincipal representing the user.</param>
    /// <param name="claimType">The type of the claim to check for.</param>
    /// <param name="claimValue">The value of the claim to check for, case insensitive.</param>
    /// <returns>True if the user has a claim with the specified type and value, otherwise false.</returns>
    public static bool HasClaimInsensitiveValue(this ClaimsPrincipal user, string claimType, string claimValue)
    {
        return user.Claims.Any(c =>
            c.Type == claimType && c.Value.Equals(claimValue, StringComparison.OrdinalIgnoreCase)
        );
    }

    /// <summary>
    /// Checks if the user has a claim with the specified type and value, ignoring case sensitivity for the value.
    /// </summary>
    /// <param name="user">The ClaimsPrincipal representing the user.</param>
    /// <param name="claimType">The type of the claim to check for.</param>
    /// <param name="claimValue">The value of the claim to check for, case insensitive. If not a string, ToString() is called on the object.</param>
    /// <returns>True if the user has a claim with the specified type and value, otherwise false.</returns>
    public static bool HasClaimInsensitiveValue(this ClaimsPrincipal user, string claimType, object claimValue)
    {
        return user.HasClaimInsensitiveValue(claimType, claimValue?.ToString() ?? string.Empty);
    }

    /// <summary>
    /// Checks if the user has a claim with the specified type and value, ignoring case sensitivity for both the type and value.
    /// </summary>
    /// <param name="user">The ClaimsPrincipal representing the user.</param>
    /// <param name="claimType">The type of the claim to check for, case insensitive.</param>
    /// <param name="claimValue">The value of the claim to check for, case insensitive.</param>
    /// <returns>True if the user has a claim with the specified type and value, otherwise false.</returns>
    public static bool HasClaimInsensitive(this ClaimsPrincipal user, string claimType, string claimValue)
    {
        return user.Claims.Any(c =>
            c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase)
            && c.Value.Equals(claimValue, StringComparison.OrdinalIgnoreCase)
        );
    }
}
