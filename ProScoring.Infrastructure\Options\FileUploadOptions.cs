namespace ProScoring.Infrastructure.Options;

/// <summary>
/// Options for file upload settings in the application.
/// </summary>
public class FileUploadOptions
{
    /// <summary>
    /// The configuration section name for file upload options.
    /// </summary>
    public const string SECTION_NAME = "FileUpload";

    /// <summary>
    /// Gets or sets the maximum size for any uploaded file in bytes.
    /// </summary>
    public int MaxSize { get; set; }

    /// <summary>
    /// Gets or sets the maximum size for uploaded burgee images in bytes.
    /// </summary>
    public int MaxBurgeeSize { get; set; }

    /// <summary>
    /// Gets or sets the maximum size for uploaded documents in bytes.
    /// </summary>
    public int MaxDocumentSize { get; set; }

    /// <summary>
    /// Gets or sets the maximum size for uploaded images in bytes.
    /// </summary>
    public int MaxImageSize { get; set; }

    /// <summary>
    /// Gets or sets the file system path where uploads are stored.
    /// </summary>
    public string UploadFilesPath { get; set; } = string.Empty;
}
