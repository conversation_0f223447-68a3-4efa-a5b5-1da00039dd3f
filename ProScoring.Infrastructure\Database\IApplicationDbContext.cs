using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.RegattaEntities;
using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Database;

public interface IApplicationDbContext
{
    DbSet<ActionHierarchy> ActionHierarchies { get; }
    DbSet<AuthAction> AuthActions { get; }
    DbSet<FileRecord> Files { get; }

    //DbSet<ImageMetadata> ImageMetadata { get; }
    DbSet<OrganizingAuthority> OrganizingAuthorities { get; }
    DbSet<OverridePermission> OverridePermissions { get; }
    DbSet<Regatta> Regattas { get; }
    DbSet<RegattaBoat> RegattaBoats { get; }
    DbSet<RegattaClass> RegattaClasses { get; }
    DbSet<RegattaCompetitor> RegattaCompetitors { get; }
    DbSet<RegattaExternalLink> RegattaExternalLinks { get; }
    DbSet<RegattaFleet> RegattaFleets { get; }
    DbSet<RegattaRating> RegattaRatings { get; }
    DbSet<RegattaRatingValue> RegattaRatingValues { get; }
    DbSet<TargetType> TargetTypes { get; }
    DbSet<UserAuthAction> UserAuthActions { get; }
    DbSet<ApplicationUser> Users { get; }

    EntityEntry<TEntity> Entry<TEntity>(TEntity entity)
        where TEntity : class;
    int SaveChanges();
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
