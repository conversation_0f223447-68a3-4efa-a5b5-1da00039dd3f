using FluentAssertions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Moq;
using Moq.AutoMock;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.Infrastructure;

[Collection(nameof(StaticIdGenerationUtilServiceForTesting))] // this causes these tests to be run in serial
public class ApplicationDbContextTests
{
    private static FileRecord SimpleFileRecord
    {
        get
        {
            var fileRecord = new FileRecord
            {
                Id = Guid.NewGuid().ToString()[..10],
                TrustedFileNameForDisplay = "test.txt",
                Path = "test/random_path.xyz",
                ContentType = "text/plain",
                // Data = [0x01, 0x02, 0x03],
                Size = 12345,
                UntrustedName = "test.txt", // Added required member
                UploadDate = new DateTimeOffset(1776, 7, 4, 12, 0, 0, TimeSpan.Zero),
            };
            return fileRecord;
        }
    }

    #region fields
    private readonly AutoMocker _mocker;
    private Mock<IIdGenerationUtilService>? _mockIdService;
    private readonly ITestOutputHelper _output;
    private readonly ServiceCollection _services;
    #endregion

    // We are only testing this with the SQLite database, using in-memory
    // This is set up in the test constructor for this test class.
    // We don't need to run the Aspire setup for these tests.
    public ApplicationDbContextTests(ITestOutputHelper output)
    {
        _output = output;

        // set up mocks for the things needed by the ApplicationDbContext
        _mocker = new AutoMocker();
        _services = new ServiceCollection();
        _services.AddTransient(_ => Mock.Of<AuthenticationStateProvider>());
        // setup the mock id service to return a string "123"
        _mockIdService = new Mock<IIdGenerationUtilService>();
        _mockIdService.Setup(m => m.GenerateId(It.IsAny<IHasAutoInsertedId>())).Returns("123");
        _services.AddSingleton<IIdGenerationUtilService>(
            StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(_mockIdService.Object)
        );
        _services.AddTransient<IValueGenerator, CustomIdValueGenerator>();
        _services.AddTransientSafeLogger<ApplicationDbContext>(_output);
        _services.AddTransientSafeLogger<CustomIdValueGenerator>(_output);
        _services.AddTransient<IDateTimeOffsetProvider>(_ => new FixedDateTimeOffsetProvider(1776, 7, 4));
        _services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlite("Data Source=:memory:;Cache=Shared");
        });
    }

    #region methods

    [Fact]
    public void ApplicationDbContext_CanCreateInstance()
    {
        // Arrange
        var serviceProvider = _services.BuildServiceProvider();
        // Act
        using var dbContext = serviceProvider.GetRequiredService<ApplicationDbContext>();
        // Assert
        dbContext.Should().NotBeNull();
    }

    [Fact]
    public void ApplicationDbContext_ShouldHaveFilesDbSet()
    {
        // Arrange
        using var dbContext = GetDbContext();
        // Act
        var files = dbContext.Files;
        // Assert
        files.Should().NotBeNull();
    }

    [Fact]
    public void File_ShouldAddAndUpdateAndRetrieveFileRecord()
    {
        // Arrange
        using var dbContext = GetDbContext();

        var fileRecord = SimpleFileRecord;

        // Act
        dbContext.Files.Add(fileRecord);
        dbContext.SaveChanges();
        var retrievedFile = dbContext.Files.Find(fileRecord.Id);

        // Assert
        retrievedFile.Should().NotBeNull();
        retrievedFile
            .Should()
            .BeEquivalentTo(
                new
                {
                    fileRecord.ContentType,
                    fileRecord.Id,
                    fileRecord.Note,
                    fileRecord.Path,
                    fileRecord.Size,
                    fileRecord.UntrustedName,
                    fileRecord.UploadDate,
                }
            );
        // TODO: add a test for the upload date.  This should probably be in the service, rather than the dbContext.
        retrievedFile!.Id.Should().Be(fileRecord.Id);
        retrievedFile.TrustedFileNameForDisplay.Should().Be("test.txt");
        retrievedFile.ContentType.Should().Be("text/plain");
    }

    [Fact]
    public void FileAdd_ShouldCallIdUtilsService_OneTime()
    {
        // this is failing when run with the other tests.
        // For this reason, I am redoing all the setup to see if that fixes it.

        // Arrange
        var dbContext = GetDbContext();
        var fileRecord = SimpleFileRecord;
        fileRecord.Id = null!;

        // Act
        dbContext.Files.Add(fileRecord);
        dbContext.SaveChanges();

        // Assert
        _mockIdService!.Verify(x => x.GenerateId(It.IsAny<IHasAutoInsertedId>()), Times.Once);
    }

    [Fact]
    public void OrganizingAuthorityAdd_ShouldCallIdUtilsService_OneTime()
    {
        // this is failing when run with the other tests.
        // For this reason, I am redoing all the setup to see if that fixes it.

        // Arrange

        var dbContext = GetDbContext();
        var oaRecord = new OrganizingAuthority
        {
            Id = null!,
            Name = "Test OA",
            Website = "https://testoa.com",
        };
        oaRecord.Id = null!;
        // Act
        dbContext.OrganizingAuthorities.Add(oaRecord);
        dbContext.SaveChanges();
        // Assert
        _mockIdService!.Verify(x => x.GenerateId(It.IsAny<IHasAutoInsertedId>()), Times.Once);
    }

    private ApplicationDbContext GetDbContext()
    {
        var serviceProvider = _services.BuildServiceProvider();
        var dbContext = serviceProvider.GetRequiredService<ApplicationDbContext>();
        dbContext.Database.OpenConnection();
        dbContext.Database.EnsureCreated();
        return dbContext;
    }

    #endregion
}
