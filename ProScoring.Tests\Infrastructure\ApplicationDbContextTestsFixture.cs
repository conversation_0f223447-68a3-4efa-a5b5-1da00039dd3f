using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.AutoMock;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Tests.Infrastructure;

public class ApplicationDbContextTestsFixture : IDisposable
{
    private ServiceCollection? Services { get; set; }

    private readonly ApplicationDbContext? _dbContext;

    public ApplicationDbContextTestsFixture()
    {
        // Set up the services for creating the db context. I don't want to use
        // the same DB context for each test, so we need to do the setup in the
        // test class, not the fixture.

        // thi code in this class is not used, but I'm keeping it for now because
        // I may want to remember how to do it later.

        return;

        // we are only testing this with the SQLite database, using in-memory

#pragma warning disable CS0162 // Unreachable code detected
        var mocker = new AutoMocker();
        var mockedAuthStateProvider = Mock.Of<AuthenticationStateProvider>();
        var mockedCustomIdValueGenerator = mocker.CreateInstance<CustomIdValueGenerator>();
        var mockedLogger = mocker.GetMock<ILogger<ApplicationDbContext>>().Object;
        // _mockDbContext is a fixture level variable to keep the inmemory database alive
        Services = new ServiceCollection();
        Services.AddSingleton(mockedAuthStateProvider);
        Services.AddSingleton(mockedCustomIdValueGenerator);
        Services.AddSingleton(mockedLogger);
        Services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlite("Data Source=:memory:;Cache=Shared");
        });

        var serviceProvider = Services.BuildServiceProvider();
        _dbContext = serviceProvider.GetRequiredService<ApplicationDbContext>();

        // make sure that the DB has been created
        _dbContext.Database.OpenConnection();
        _dbContext.Database.EnsureCreated();
#pragma warning restore CS0162 // Unreachable code detected
    }

    #region IDisposable
    private bool _disposed;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }
        if (disposing)
        {
            // Dispose managed resources
            _dbContext?.Dispose();
        }
        // Dispose unmanaged resources
        _disposed = true;
    }

    #endregion
}
