using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.AutoMock;
using NSubstitute;
using ProScoring.Blazor.Controllers;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.BusinessLogic.Services;
using ProScoring.Domain.Dtos;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.WebApi;

[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
// this causes these tests to be run in serial.
public class OrganizingAuthorityControllerIntegrationTests
{
    private const string TEST_FILE_DIRECTORY = "\\_Temp\\ProScoring.Tests_WebApi";

    // may need to be set in the test so services can be configured differently.
    private readonly AutoMocker _mocker;

    private readonly ITestOutputHelper _output;
    private readonly IServiceProvider _serviceProvider;
    private readonly ServiceCollection _services;

    public OrganizingAuthorityControllerIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        _services = new ServiceCollection();
        _mocker = new AutoMocker();

        // setup tho AuthenticationStateProvider. We will need this to return a user and id when appropriate.
        _services.AddScopedSafeLogger<AuthenticationStateProvider>(output);
        _services.AddScopedSafeLogger<ApplicationDbContext>(output);
        _services.AddScopedSafeLogger<CustomIdValueGenerator>(output);
        _services.AddScopedSafeLogger<GuidishIdGenerationUtilService>(output);
        _services.AddScopedSafeLogger<OrganizingAuthorityController>(output);
        _services.AddScopedSafeLogger<OrganizingAuthorityService>(output);
        _services.AddScopedSafeLogger<FileService>(output);
        _services.AddScopedSafeLogger<ProScoringAuthorizationService>(output);
        _services.AddScoped(_ => _mocker.CreateInstance<AuthenticationStateProvider>());
        _services.AddScoped<IIdGenerationUtilService>(_ =>
        {
            return StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(
                _serviceProvider!.GetService<GuidishIdGenerationUtilService>()
                    ?? throw new InvalidOperationException("GuidishIdGenerationUtilService not found.")
            );
        });
        _services.AddScoped<IDateTimeOffsetProvider>(_ => _mocker.CreateInstance<DateTimeOffsetProviderCpuTime>());
        _services.AddScoped<GuidishIdGenerationUtilService>();
        _services.AddScoped<IValueGenerator, CustomIdValueGenerator>();
        var mockAuthStateProvider = new Mock<AuthenticationStateProvider>();
        var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, "userid1233") };
        var identity = new ClaimsIdentity(claims, "TestAuthType");
        var user = new ClaimsPrincipal(identity);
        var mockHttpContextAccessor = Substitute.For<IHttpContextAccessor>();
        _services.AddScoped(_ => mockHttpContextAccessor);
        var mockAuthService = Substitute.For<IAuthorizationService>();
        mockAuthService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));
        _services.AddScoped(_ => mockAuthService);
        mockAuthStateProvider.Setup(m => m.GetAuthenticationStateAsync()).ReturnsAsync(new AuthenticationState(user));
        _services.AddScoped(_ => mockAuthStateProvider.Object);
        _services.AddScoped(_ =>
            Substitute.For<ServiceAuthorizationHelper>(
                mockAuthService,
                mockHttpContextAccessor,
                Substitute.For<ILogger<ServiceAuthorizationHelper>>()
            )
        );
        _services.AddDbContext<IApplicationDbContext, ApplicationDbContext>(
            options =>
            {
                options.UseSqlite("Data Source=:memory:;Cache=Shared");
            },
            ServiceLifetime.Scoped
        );
        _services.AddScoped<IOrganizingAuthorityService, OrganizingAuthorityService>();
        _services.AddTransient(_ =>
        {
            var mock = new Mock<IOptions<FileUploadOptions>>();
            mock.Setup(m => m.Value).Returns(new FileUploadOptions { MaxSize = 1024 * 5 });
            return mock.Object;
        });
        _services.AddScoped<IFileService, FileService>();
        // for picking the file save directory we need IHostEnvironment
        _services.AddTransient(_ =>
        {
            var mock = new Mock<IHostEnvironment>();
            mock.Setup(m => m.ContentRootPath).Returns(TEST_FILE_DIRECTORY);
            return mock.Object;
        });
        // Configure settings
        var inMemorySettings = new Dictionary<string, string?> { { "ASPNETCORE_ENVIRONMENT", "Development" } };
        _services.AddScoped<IConfiguration>(_ =>
        {
            return new ConfigurationBuilder().AddInMemoryCollection(inMemorySettings).Build();
        });
        _services.AddScoped(_ => new FileUploadOptions() { MaxSize = 1024 * 5 });

        _services.AddScoped<IProScoringAuthorizationService, ProScoringAuthorizationService>();

        //// item under test
        _services.AddScoped<OrganizingAuthorityController>();

        _serviceProvider = _services.BuildServiceProvider();
        // make sure that the db was created
        var dbContext = _serviceProvider.GetService<ApplicationDbContext>();
        dbContext.Should().NotBeNull();
        dbContext!.Database.OpenConnection();
        dbContext!.Database.EnsureCreated();
    }

    [Fact]
    public async Task Create_ReturnsBadRequest_WhenDtoIsNull()
    {
        // Arrange
        var controllerUnderTest =
            _serviceProvider.GetService<OrganizingAuthorityController>()
            ?? throw new InvalidOperationException("OrganizingAuthorityController not found.");

        // Act
        var result = await controllerUnderTest!.Create(null);

        // Assert
        result.Should().BeOfType<BadRequestResult>();
    }

    [Fact(Skip = "net completed. Needs better setup.")]
    public async Task Create_ReturnsCreatedAtActionResult()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto
        {
            Name = "Test Authority",
            BurgeeDataUri = "data:image/png;base64,AAESIg==",
            BurgeeFileName = "burgee.png",
        };
        var fileUploadResult = new FileUploadResult { TrustedFileNameForDisplay = "burgee.png", Id = "thisId" };

        // Create partial mock of FileService
        //var mockFileService = new Mock<FileService>(_serviceProvider.GetService<IHostEnvironment>()!,
        //    _serviceProvider.GetService<IConfiguration>()!);
        //mockFileService
        //    .Setup(f => f.UploadFromDataUriAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
        //.ReturnsAsync(new FileUploadResult());
        //var mockOrganizingAuthorityService = _mocker.GetMock<IOrganizingAuthorityService>();
        //mockOrganizingAuthorityService
        //    .Setup(s => s.CreateAsync(It.IsAny<OrganizingAuthority>()))
        //    .Returns(Task.CompletedTask);
        //var mockAuthorizationService = _mocker.GetMock<AuthorizationService>();
        //mockAuthorizationService
        //    .Setup(a => a.CreateUserAuthActionAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
        //    .ReturnsAsync(new UserAuthAction());
        //_services.AddScoped(_ => mockFileService.Object);
        //var serviceProvider = _services.BuildServiceProvider();

        var controllerUnderTest =
            _serviceProvider.GetService<OrganizingAuthorityController>()
            ?? throw new InvalidOperationException("OrganizingAuthorityController not found.");

        // Act
        var result = await controllerUnderTest.Create(dto);

        // Assert
        var createdAtActionResult = result.Should().BeOfType<CreatedAtActionResult>().Subject;
        createdAtActionResult.ActionName.Should().Be(nameof(controllerUnderTest.GetById));
    }
    /*
            [Fact]
            public async Task Delete_ReturnsNoContent_WhenDeleteIsSuccessful()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Test Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);
                mockOrganizingAuthorityService.Setup(s => s.DeleteAsync("1")).Returns(Task.CompletedTask);

                // Act
                var result = await controllerUnderTest.Delete("1");

                // Assert
                result.Should().BeOfType<NoContentResult>();
            }

            [Fact]
            public async Task Delete_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                mockOrganizingAuthorityService
                    .Setup(s => s.GetByIdAsync(It.IsAny<string>()))
                    .ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.Delete("1");

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }

            [Fact]
            public async Task GetAll_ReturnsOkResult_WithListOfAuthorities()
            {
                // Arrange
                var authorities = new List<OrganizingAuthority>
                {
                    new OrganizingAuthority { Id = "1", Name = "Test Authority" },
                };
                mockOrganizingAuthorityService.Setup(s => s.GetAllAsync()).ReturnsAsync(authorities);

                // Act
                var result = await controllerUnderTest.GetAll();

                // Assert
                var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
                var returnValue = okResult.Value.Should().BeOfType<List<OrganizingAuthority>>().Subject;
                returnValue.Count.Should().Be(1);
            }

            [Fact]
            public async Task GetById_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                mockOrganizingAuthorityService
                    .Setup(s => s.GetByIdAsync(It.IsAny<string>()))
                    .ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.GetById("1");

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }

            [Fact]
            public async Task GetById_ReturnsOkResult_WithAuthority()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Test Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);

                // Act
                var result = await controllerUnderTest.GetById("1");

                // Assert
                var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
                var returnValue = okResult.Value.Should().BeOfType<OrganizingAuthority>().Subject;
                returnValue.Id.Should().Be("1");
            }

            [Fact]
            public async Task Update_ReturnsBadRequest_WhenIdDoesNotMatch()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };

                // Act
                var result = await controllerUnderTest.Update("2", authority);

                // Assert
                result.Should().BeOfType<BadRequestResult>();
            }

            [Fact]
            public async Task Update_ReturnsNoContent_WhenUpdateIsSuccessful()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync(authority);
                mockOrganizingAuthorityService.Setup(s => s.UpdateAsync(authority)).Returns(Task.CompletedTask);

                // Act
                var result = await controllerUnderTest.Update("1", authority);

                // Assert
                result.Should().BeOfType<NoContentResult>();
            }

            [Fact]
            public async Task Update_ReturnsNotFound_WhenAuthorityDoesNotExist()
            {
                // Arrange
                var authority = new OrganizingAuthority { Id = "1", Name = "Updated Authority" };
                mockOrganizingAuthorityService.Setup(s => s.GetByIdAsync("1")).ReturnsAsync((OrganizingAuthority?)null);

                // Act
                var result = await controllerUnderTest.Update("1", authority);

                // Assert
                result.Should().BeOfType<NotFoundResult>();
            }
        */
}
