namespace ProScoring.Infrastructure.ServiceInterfaces
{
    /// <summary>
    /// Defines a service for retrieving the hierarchical parentage of a target.
    /// The hierarchy is typically ordered from the most specific parent upwards.
    /// </summary>
    public interface ITargetHierarchyService
    {
        /// <summary>
        /// Gets the ordered list of parent target IDs for a given target.
        /// The list should be ordered from the immediate parent to the furthest ancestor.
        /// The initial targetId itself should not be included in this list.
        /// </summary>
        /// <param name="targetId">The ID of the target for which to retrieve the hierarchy.</param>
        /// <returns>
        /// An asynchronous task that returns an enumerable collection of strings,
        /// representing the parent target IDs in order from immediate parent to furthest ancestor.
        /// Returns an empty collection if the target has no parents or is not found.
        /// </returns>
        Task<IEnumerable<string>> GetTargetHierarchyAsync(string targetId);
    }
}
