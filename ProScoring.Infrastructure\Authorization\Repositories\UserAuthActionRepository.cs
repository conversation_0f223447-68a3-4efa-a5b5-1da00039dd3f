using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Authorization.Repositories.Interfaces;
using ProScoring.Infrastructure.Database;

namespace ProScoring.Infrastructure.Authorization.Repositories
{
    /// <summary>
    /// Implements the repository for managing UserAuthAction entities.
    /// </summary>
    public class UserAuthActionRepository : IUserAuthActionRepository
    {
        private readonly IApplicationDbContext _context;
        private readonly ILogger<UserAuthActionRepository> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserAuthActionRepository"/> class.
        /// </summary>
        /// <param name="context">The application database context.</param>
        /// <param name="logger">The logger.</param>
        public UserAuthActionRepository(IApplicationDbContext context, ILogger<UserAuthActionRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<UserAuthAction?> GetByKeyAsync(string userId, string targetId, string actionName)
        {
            _logger.LogTrace("Getting UserAuthAction by key: UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}", userId, targetId, actionName);
            // Key order for FindAsync as defined by [Column(Order=X)] attributes in UserAuthAction.cs:
            // AuthActionName (Order = 10)
            // UserId (Order implicitly after, as part of FK to ApplicationUser)
            // TargetId (Order = 30)
            // The compound key definition in IHasCompoundKey also uses item.AuthActionName, item.UserId, item.TargetId.
            // Thus, the order for FindAsync should be actionName, userId, targetId.
            return await _context.UserAuthActions.FindAsync(actionName, userId, targetId);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<UserAuthAction>> GetByUserAsync(string userId)
        {
            _logger.LogTrace("Getting UserAuthActions by UserId={UserId}", userId);
            return await _context.UserAuthActions
                .Where(uaa => uaa.UserId == userId)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<UserAuthAction>> GetByUserAndTargetAsync(string userId, string targetId)
        {
            _logger.LogTrace("Getting UserAuthActions by UserId={UserId} and TargetId={TargetId}", userId, targetId);
            return await _context.UserAuthActions
                .Where(uaa => uaa.UserId == userId && uaa.TargetId == targetId)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task AddAsync(UserAuthAction userAuthAction)
        {
            _logger.LogTrace("Adding new UserAuthAction: UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}", 
                userAuthAction.UserId, userAuthAction.TargetId, userAuthAction.AuthActionName);
            await _context.UserAuthActions.AddAsync(userAuthAction);
            // SaveChangesAsync should be called by a Unit of Work or the service layer.
        }

        /// <inheritdoc/>
        public async Task AddRangeAsync(IEnumerable<UserAuthAction> userAuthActions)
        {
            _logger.LogTrace("Adding range of {Count} UserAuthActions", userAuthActions.Count());
            await _context.UserAuthActions.AddRangeAsync(userAuthActions);
            // SaveChangesAsync by Unit of Work pattern.
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteByKeyAsync(string userId, string targetId, string actionName)
        {
            _logger.LogTrace("Deleting UserAuthAction by key: UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}", userId, targetId, actionName);
            // Key order for FindAsync: actionName, userId, targetId
            var entity = await _context.UserAuthActions.FindAsync(actionName, userId, targetId);
            if (entity != null)
            {
                _context.UserAuthActions.Remove(entity);
                // SaveChangesAsync by Unit of Work pattern.
                return true;
            }
            _logger.LogDebug("UserAuthAction not found for deletion with key: UserId={UserId}, TargetId={TargetId}, ActionName={ActionName}", userId, targetId, actionName);
            return false;
        }

        /// <inheritdoc/>
        public Task DeleteRangeAsync(IEnumerable<UserAuthAction> userAuthActions)
        {
            _logger.LogTrace("Deleting range of {Count} UserAuthActions", userAuthActions.Count());
            _context.UserAuthActions.RemoveRange(userAuthActions);
            // SaveChangesAsync by Unit of Work pattern.
            return Task.CompletedTask; // RemoveRange is synchronous for EF Core
        }
    }
}
