using FluentAssertions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Moq;
using ProScoring.Blazor.Controllers;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.WebApi;

[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
// this causes these tests to be run in serial
public class FileControllerTests : IDisposable
{
    private const string TEST_FILE_DIRECTORY = "\\_Temp\\ProScoring.Tests_WebApi";

    private readonly ITestOutputHelper _output;
    private readonly IServiceProvider _serviceProvider;
    private readonly ServiceCollection _services;

    public FileControllerTests(ITestOutputHelper output)
    {
        _output = output;
        _services = new ServiceCollection();
        _services.AddTransientSafeLogger<ApplicationDbContext>(_output);
        _services.AddTransientSafeLogger<FileController>(_output);
        _services.AddTransientSafeLogger<GuidishIdGenerationUtilService>(_output);
        _services.AddTransientSafeLogger<CustomIdValueGenerator>(_output);
        _services.AddTransientSafeLogger<FileService>(_output);
        _services.AddTransientSafeLogger<ApplicationUser>(_output);

        // Configure the ID generator code
        _services.AddScoped<IDateTimeOffsetProvider>(_ => new FixedDateTimeOffsetProvider(1776, 7, 4, 12, 1));

        _services.AddSingleton<IIdGenerationUtilService>(_ =>
        {
            return StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(
                _serviceProvider!.GetService<GuidishIdGenerationUtilService>()!
            );
        });
        _services.AddTransient<GuidishIdGenerationUtilService>();
        _services.AddTransient<IValueGenerator, CustomIdValueGenerator>();
        _services.AddTransient(_ => Mock.Of<AuthenticationStateProvider>());
        _services.AddDbContext<ApplicationDbContext>(
            options =>
            {
                // To use the id generation functionality, we need a
                // real database and not the in-memory database that uses simple linq.
                options.UseSqlite("Data Source=:memory:");
            },
            ServiceLifetime.Scoped
        );

        // for picking the file save directory we need IHostEnvironment
        _services.AddTransient(_ =>
        {
            var mock = new Mock<IHostEnvironment>();
            mock.Setup(m => m.ContentRootPath).Returns(TEST_FILE_DIRECTORY);
            return mock.Object;
        });
        _services.AddTransient(_ =>
        {
            var mock = new Mock<IOptions<FileUploadOptions>>();
            mock.Setup(m => m.Value).Returns(new FileUploadOptions { MaxSize = 1024 * 5 });
            return mock.Object;
        });
        _services.AddScoped<IFileService, FileService>();

        // Configure settings
        var inMemorySettings = new Dictionary<string, string?> { { "ASPNETCORE_ENVIRONMENT", "Development" } };
        _services.AddTransient<IConfiguration>(_ =>
        {
            return new ConfigurationBuilder().AddInMemoryCollection(inMemorySettings).Build();
        });
        _services.AddTransient<FileController>();

        _serviceProvider = _services.BuildServiceProvider();
        // make sure that the db is created
        var dbContext = _serviceProvider.GetService<ApplicationDbContext>()!;
        dbContext.Should().NotBeNull();
        dbContext.Database.OpenConnection();
        dbContext.Database.EnsureCreated();
    }

    #region methods

    [Fact]
    public async Task Download_FileDoesNotExist_ReturnsNotFound()
    {
        // Act
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var result = await controller.Download("nonexistent");

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
        Assert.Equal("File not found.", notFoundResult.Value);
    }

    [Fact]
    public async Task Download_FileExists_ReturnsFile()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var randomData = new byte[1024];
        new Random().NextBytes(randomData);
        var fileName = Path.GetTempFileName();
        var file = new FormFile(new MemoryStream(randomData), 0, 1024, "Data", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };
        var uploadResult = await controller.Upload(file, "note");
        var uploadResultValue = (uploadResult.Result.As<OkObjectResult>().Value as FileUploadResult)!;
        uploadResultValue.Should().NotBeNull();
        var fileId = uploadResultValue.Id!;
        var dateTimeProvider = _serviceProvider.GetService<IDateTimeOffsetProvider>()!;
        _output.WriteLine($"File ID: {fileId}. DateTime: {dateTimeProvider.UtcNow}");
        fileId.Should().NotBeNullOrEmpty();

        // Act
        var result = await controller.Download(fileId);

        // Assert
        var fileResult = Assert.IsType<FileStreamResult>(result);
        Assert.Equal("text/plain", fileResult.ContentType);
        Assert.Equal(Path.GetFileName(fileName), fileResult.FileDownloadName);
        using (var memoryStream = new MemoryStream())
        {
            await fileResult.FileStream.CopyToAsync(memoryStream);
            Assert.Equal(randomData, memoryStream.ToArray());
        }
    }

    [Fact]
    public async Task Get_HelloWorld_ReturnsHelloWorld()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        // Act
        //////////
        var result = await controller.HelloWorld();

        // Assert
        //////////
        result.Result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        result.Result.As<OkObjectResult>().Value.Should().Be("Hello World");
    }

    [Theory]
    [InlineData("file with space.txt", "file with space.txt")]
    [InlineData("<b>bold</b>FileName.txt", "b&gt;FileName.txt")]
    [InlineData("testFile.text", "testFile.text")]
    public async Task GetById_FileUploaded_ReturnsId(string fileName, string encodedFileName)
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var randomData = new byte[1024];
        new Random().NextBytes(randomData);
        var file = new FormFile(new MemoryStream(randomData), 0, 1024, "Data", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };
        var uploadResult = await controller.Upload(file, "note");
        var uploadResultValue = (uploadResult.Result.As<OkObjectResult>().Value as FileUploadResult)!;
        uploadResultValue.Should().NotBeNull();
        var fileId = uploadResultValue.Id!;
        var dateTimeProvider = _serviceProvider.GetService<IDateTimeOffsetProvider>()!;
        _output.WriteLine($"File ID: {fileId}. DateTime: {dateTimeProvider.UtcNow}");
        fileId.Should().NotBeNullOrEmpty();

        // Act
        //////////
        var result = await controller.Get(fileId);

        // Assert
        //////////
        result.Should().NotBeNull();
        result.Should().BeOfType<ActionResult<FileRecord>>();
        result.Result.Should().BeOfType<OkObjectResult>();
        var fileRecord = result.Result.As<OkObjectResult>().Value as FileRecord;
        fileRecord.Should().NotBeNull();
        fileRecord
            .Should()
            .BeEquivalentTo(
                new { UntrustedName = Path.GetFileName(fileName), TrustedFileNameForDisplay = encodedFileName },
                "Result did not match expected"
            );
        fileRecord!.Id.Should().StartWith("F6fEE5s"); // because we know the date provided.
    }

    [Fact]
    public async Task Post_GoodbyeWorld_ReturnsGoodByeWorldAsync()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        // Act
        //////////
        var result = await controller.GoodbyeWorld();

        // Assert
        //////////
        result.Result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        result.Result.As<OkObjectResult>().Value.Should().Be("Goodbye World");
    }

    [Fact]
    public async Task Upload_EmptyFile_ReturnsBadRequest()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var file = new FormFile(new MemoryStream(), 0, 0, "Data", "empty.txt");

        // Act
        //////////
        var result = await controller.Upload(file, "note");

        // Assert
        //////////
        result.Result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        result
            .Result.As<BadRequestObjectResult>()
            .Value.Should()
            .BeEquivalentTo(new { ErrorCode = (int)FileUploadResult.ErrorCodes.FileEmpty });
    }

    [Fact]
    public async Task Upload_FileLangerThanLimit_ReturnsBadRequest()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var file = new FormFile(new MemoryStream(new byte[1024 * 6]), 0, 1024 * 6, "Data", "large.txt");

        // Act
        //////////
        var result = await controller.Upload(file, "note");

        // Assert
        //////////
        result.Result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        result
            .Result.As<BadRequestObjectResult>()
            .Value.Should()
            .BeEquivalentTo(new { ErrorCode = (int)FileUploadResult.ErrorCodes.FileTooLarge });
    }

    [Theory]
    [InlineData("file with space.txt", "file with space.txt")]
    [InlineData("<b>bold</b>FileName.txt", "b&gt;FileName.txt")]
    public async Task Upload_FileSpecialCharactersInName_HtmlEncodesFileName(string fileName, string encodedFileName)
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var file = new FormFile(new MemoryStream(new byte[1024]), 0, 1024, "Data", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };

        // Act
        //////////
        var result = await controller.Upload(file, "note");

        // Assert
        //////////
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        result
            .Result.As<OkObjectResult>()
            .Value.Should()
            .BeEquivalentTo(new { TrustedFileNameForDisplay = encodedFileName }, "Result did not match expected");
    }

    [Fact]
    public async Task Upload_FileWithPath_TrustedFileNameForDisplayDoesNotContainPath()
    {
        // Arrange
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var fileName = Path.Combine(Path.GetTempPath(), "testFile.txt");
        var file = new FormFile(new MemoryStream(new byte[1024]), 0, 1024, "Data", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };

        // Act
        var uploadResult = await controller.Upload(file, "note");
        var uploadResultValue = (uploadResult.Result.As<OkObjectResult>().Value as FileUploadResult)!;
        uploadResultValue.Should().NotBeNull();
        var fileId = uploadResultValue.Id!;

        var result = await controller.Get(fileId);
        var fileRecord = result.Result.As<OkObjectResult>().Value as FileRecord;
        fileRecord.Should().NotBeNull();

        // Assert
        fileRecord!.TrustedFileNameForDisplay.Should().Be("testFile.txt");
    }

    [Fact]
    public async Task Upload_FileWithPath_UntrustedNameDoesNotContainPath()
    {
        // Arrange
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var fileName = Path.Combine(Path.GetTempPath(), "testFile.txt");
        var file = new FormFile(new MemoryStream(new byte[1024]), 0, 1024, "Data", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };

        // Act
        var uploadResult = await controller.Upload(file, "note");
        var uploadResultValue = (uploadResult.Result.As<OkObjectResult>().Value as FileUploadResult)!;
        uploadResultValue.Should().NotBeNull();
        var fileId = uploadResultValue.Id!;

        var result = await controller.Get(fileId);
        var fileRecord = result.Result.As<OkObjectResult>().Value as FileRecord;
        fileRecord.Should().NotBeNull();

        // Assert
        fileRecord!.UntrustedName.Should().Be("testFile.txt");
    }

    [Fact]
    public async Task Upload_NullFile_ReturnsBadRequest()
    {
        // Arrange
        //////////
        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        // Act
        //////////
        var result = await controller.Upload(null!, "note");

        // Assert
        //////////
        result.Result.Should().NotBeNull();
        result.Result.Should().BeOfType<BadRequestObjectResult>();
        result
            .Result.As<BadRequestObjectResult>()
            .Value.Should()
            .BeEquivalentTo(new { ErrorCode = (int)FileUploadResult.ErrorCodes.FileNull });
    }

    [Fact]
    public async Task Upload_ValidFileData_ReturnsValidFileId()
    {
        // Arrange
        //////////

        var controller = _serviceProvider.GetService<FileController>()!;
        controller.Should().NotBeNull();

        var file = new FormFile(new MemoryStream(new byte[1024]), 0, 1024, "Data", "testFile.txt")
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain",
        };

        // Act
        //////////
        var result = await controller.Upload(file, "note");

        // Assert
        //////////
        result.Should().NotBeNull();
        result.Result.Should().BeOfType<OkObjectResult>();
        var uploadResult = result.Result.As<OkObjectResult>().Value as FileUploadResult;
        uploadResult.Should().NotBeNull();
        uploadResult!.Id.Should().NotBeNullOrEmpty();
    }

    // delete all files in the TEST_FILE_DIRECTORY
    private void DeleteTestFileDirectory()
    {
        if (Directory.Exists(TEST_FILE_DIRECTORY))
        {
            _output.WriteLine($"Deleting directory: {TEST_FILE_DIRECTORY}");
            // Retry deleting the directory in case of IOException
            for (int i = 0; i < 2; i++)
            {
                try
                {
                    Directory.Delete(TEST_FILE_DIRECTORY, true);
                    break;
                }
                catch (IOException ex)
                {
                    _output.WriteLine($"IOException occurred on retry {i + 1}: {ex.Message}. Retrying...");
                    Thread.Sleep(1000); // Wait for 1 second before retrying
                }
            }
        }
    }

    private void DisposeOfServices()
    {
        foreach (
            var disposable in _services.Select(sd => sd.ImplementationInstance as IDisposable).Where(sd => sd != null)
        )
        {
            _output.WriteLine($"Disposing {disposable!.GetType().Name}");
            disposable.Dispose();
        }
    }

    #endregion

    #region IDisposable
    private bool _disposed = false;

    ~FileControllerTests()
    {
        Dispose(false);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                DisposeOfServices();
                DeleteTestFileDirectory();

                // Clean up any unmanaged resources if needed
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    #endregion
}
