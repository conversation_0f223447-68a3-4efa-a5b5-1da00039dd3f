@using ProScoring.Blazor.Extensions
@using ProScoring.Domain.Dtos
@inject NavigationManager NavigationManager

@if (Authority != null)
{
    @if (DisplayMode == DisplayMode.Card)
    {
        <RadzenCard Style="@(EnableNavigationOnClick ? CardStyle + " cursor: pointer;" : CardStyle)" class="fixed-height-card" @attributes="@("oa-info-card".AsTestId())" @onclick="HandleClick">
            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem" AlignItems="AlignItems.Center">
                @if (!string.IsNullOrEmpty(Authority.ImageId))
                {
                    <RadzenImage Path="@($"/api/file/download/{Authority.ImageId}")"
                        AlternateText="@($"{Authority.Name} Burgee")"
                        Style="width: 100px; height: 100px; object-fit: contain;"
                        @attributes="@("oa-burgee-image".AsTestId())" />
                }
                else
                {
                    <RadzenIcon Icon="hide_image" Style="font-size: 100px; color: var(--rz-text-disabled-color);" />
                }

                <RadzenText TextStyle="TextStyle.H4" @attributes="@("oa-name".AsTestId())">
                    @Authority.Name
                </RadzenText>

                @if (ShowAdditionalInfo)
                {
                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem" AlignItems="AlignItems.Center">
                        @if (!string.IsNullOrEmpty(Authority.Email))
                        {
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                <RadzenIcon Icon="email" />
                                <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-email".AsTestId())">
                                    @Authority.Email
                                </RadzenText>
                            </RadzenStack>
                        }

                        @if (!string.IsNullOrEmpty(Authority.Phone))
                        {
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                <RadzenIcon Icon="phone" />
                                <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-phone".AsTestId())">
                                    @Authority.Phone
                                </RadzenText>
                            </RadzenStack>
                        }

                        @if (!string.IsNullOrEmpty(Authority.Website))
                        {
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                <RadzenIcon Icon="language" />
                                <a href="@Authority.Website" target="_blank" @attributes="@("oa-website-link".AsTestId())">
                                    @Authority.Website
                                </a>
                            </RadzenStack>
                        }
                    </RadzenStack>
                }

                <div style="flex-grow: 1;"></div>
                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem"
                    JustifyContent="JustifyContent.Center"
                    Style="margin-top: auto;"
                     class="location-info">
                    @if (!string.IsNullOrEmpty(Authority.City) )
                    {
                        <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-city".AsTestId())">
                            @Authority.City
                        </RadzenText>
                    }

                    @if (!string.IsNullOrEmpty(Authority.City) && !string.IsNullOrEmpty(Authority.State))
                    {
                        <RadzenText TextStyle="TextStyle.Body1">|</RadzenText>
                    }

                    @if (!string.IsNullOrEmpty(Authority.State))
                    {
                        <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-state".AsTestId())">
                            @Authority.State
                        </RadzenText>
                    }

                    @if (!string.IsNullOrEmpty(Authority.Country)
                        && Authority.Country != "USA"
                        && Authority.Country != "United States"
                        && Authority.Country != "United States of America")
                    {
                        if (!string.IsNullOrEmpty(Authority.City) || !string.IsNullOrEmpty(Authority.State))
                        {
                            <RadzenText TextStyle="TextStyle.Body1">|</RadzenText>
                        }
                        <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-country".AsTestId())">
                            @Authority.Country
                        </RadzenText>
                    }
                    else if (!string.IsNullOrEmpty(Authority.Country))
                    {
                        <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-country".AsTestId())" style="display: none;">
                            @Authority.Country
                        </RadzenText>
                    }
                </RadzenStack>

            </RadzenStack>
        </RadzenCard>
    }
    else
    {
        <RadzenRow Style="@(EnableNavigationOnClick ? RowStyle + " cursor: pointer;" : RowStyle)" @attributes="@("oa-info-row".AsTestId())" @onclick="HandleClick">
            <RadzenColumn Size="12" SizeMD="2" Style="display: flex; align-items: center; justify-content: center;">
                @if (!string.IsNullOrEmpty(Authority.ImageId))
                {
                    <RadzenImage Path="@($"/api/file/download/{Authority.ImageId}")"
                        AlternateText="@($"{Authority.Name} Burgee")"
                        Style="width: 50px; height: 50px; object-fit: contain;"
                        @attributes="@("oa-burgee-image-small".AsTestId())" />
                }
                else
                {
                    <RadzenIcon Icon="hide_image" Style="font-size: 50px; color: var(--rz-text-disabled-color);" />
                }
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="10">
                <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem">
                    <RadzenText TextStyle="TextStyle.H5" @attributes="@("oa-name".AsTestId())">
                        @Authority.Name
                    </RadzenText>

                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                        @if (!string.IsNullOrEmpty(Authority.City))
                        {
                            <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-city".AsTestId())">
                                @Authority.City
                            </RadzenText>
                        }

                        @if (!string.IsNullOrEmpty(Authority.City) && !string.IsNullOrEmpty(Authority.State))
                        {
                            <RadzenText TextStyle="TextStyle.Body1">|</RadzenText>
                        }

                        @if (!string.IsNullOrEmpty(Authority.State))
                        {
                            <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-state".AsTestId())">
                                @Authority.State
                            </RadzenText>
                        }

                        @if (!string.IsNullOrEmpty(Authority.Country)
                            && Authority.Country != "USA"
                            && Authority.Country != "United States"
                            && Authority.Country != "United States of America")
                        {
                            if (!string.IsNullOrEmpty(Authority.City) || !string.IsNullOrEmpty(Authority.State))
                            {
                                <RadzenText TextStyle="TextStyle.Body1">|</RadzenText>
                            }
                            <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-country".AsTestId())">
                                @Authority.Country
                            </RadzenText>
                        }
                        else if (!string.IsNullOrEmpty(Authority.Country))
                        {
                            <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-country".AsTestId())" style="display: none;">
                                @Authority.Country
                            </RadzenText>
                        }
                    </RadzenStack>

                    @if (ShowAdditionalInfo)
                    {
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                            @if (!string.IsNullOrEmpty(Authority.Email))
                            {
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                    <RadzenIcon Icon="email" />
                                    <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-email".AsTestId())">
                                        @Authority.Email
                                    </RadzenText>
                                </RadzenStack>
                            }

                            @if (!string.IsNullOrEmpty(Authority.Phone))
                            {
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                    <RadzenIcon Icon="phone" />
                                    <RadzenText TextStyle="TextStyle.Body1" @attributes="@("oa-phone".AsTestId())">
                                        @Authority.Phone
                                    </RadzenText>
                                </RadzenStack>
                            }

                            @if (!string.IsNullOrEmpty(Authority.Website))
                            {
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" AlignItems="AlignItems.Center">
                                    <RadzenIcon Icon="language" />
                                    <a href="@Authority.Website" target="_blank" @attributes="@("oa-website-link".AsTestId())">
                                        @Authority.Website
                                    </a>
                                </RadzenStack>
                            }
                        </RadzenStack>
                    }
                </RadzenStack>
            </RadzenColumn>
        </RadzenRow>
    }
}
else
{
    <RadzenCard Style="@CardStyle" @attributes="@("oa-info-placeholder".AsTestId())">
        <RadzenStack Orientation="Orientation.Vertical" Gap="1rem" AlignItems="AlignItems.Center">
            <RadzenIcon Icon="hide_image" Style="font-size: 100px; color: var(--rz-text-disabled-color);" />
            <RadzenText TextStyle="TextStyle.H5">No organizing authority information available</RadzenText>
        </RadzenStack>
    </RadzenCard>
}

@code {
    #region properties
    /// <summary>
    /// Gets or sets the organizing authority information to display.
    /// </summary>
    [Parameter]
    public OrganizingAuthorityInfoDto? Authority { get; set; }

    /// <summary>
    /// Gets or sets the CSS style for the card display mode.
    /// </summary>
    [Parameter]
    public string CardStyle { get; set; } = "width: 100%; max-width: 400px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;";

    /// <summary>
    /// Gets or sets the display mode for the component.
    /// </summary>
    [Parameter]
    public DisplayMode DisplayMode { get; set; } = DisplayMode.Card;

    /// <summary>
    /// Gets or sets the CSS style for the row display mode.
    /// </summary>
    [Parameter]
    public string RowStyle { get; set; } = "width: 100%; padding: 1rem; border-bottom: 1px solid var(--rz-border-color);";

    /// <summary>
    /// Gets or sets whether to show additional information such as email, phone, and website.
    /// </summary>
    [Parameter]
    public bool ShowAdditionalInfo { get; set; } = true;
    
    /// Gets or sets whether navigation is enabled when the component is clicked.
    /// </summary>
    [Parameter]
    public bool EnableNavigationOnClick { get; set; } = false;
    #endregion Properties

    private void HandleClick()
    {
        if (EnableNavigationOnClick && Authority != null && !string.IsNullOrEmpty(Authority.Id))
        {
            NavigationManager.NavigateTo($"/organizingauthorities/details?id={Authority.Id}");
        }
    }
}
