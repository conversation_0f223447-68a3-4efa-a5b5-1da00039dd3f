using System.Collections.Generic;
using System.Threading.Tasks;
using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Authorization.Repositories.Interfaces
{
    /// <summary>
    /// Defines the contract for a repository managing UserAuthAction entities.
    /// </summary>
    public interface IUserAuthActionRepository
    {
        /// <summary>
        /// Gets a specific UserAuthAction by its composite key.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <param name="actionName">The action's name.</param>
        /// <returns>The UserAuthAction if found; otherwise, null.</returns>
        Task<UserAuthAction?> GetByKeyAsync(string userId, string targetId, string actionName);

        /// <summary>
        /// Gets all UserAuthActions for a specific user.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <returns>An enumerable collection of UserAuthActions for the user.</returns>
        Task<IEnumerable<UserAuthAction>> GetByUserAsync(string userId);

        /// <summary>
        /// Gets all UserAuthActions for a specific user and target.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <returns>An enumerable collection of UserAuthActions for the user and target.</returns>
        Task<IEnumerable<UserAuthAction>> GetByUserAndTargetAsync(string userId, string targetId);

        /// <summary>
        /// Adds a new UserAuthAction to the repository.
        /// </summary>
        /// <param name="userAuthAction">The UserAuthAction to add.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task AddAsync(UserAuthAction userAuthAction);

        /// <summary>
        /// Adds a range of new UserAuthActions to the repository.
        /// </summary>
        /// <param name="userAuthActions">The collection of UserAuthActions to add.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task AddRangeAsync(IEnumerable<UserAuthAction> userAuthActions);

        /// <summary>
        /// Deletes a UserAuthAction from the repository by its composite key.
        /// </summary>
        /// <param name="userId">The user's ID.</param>
        /// <param name="targetId">The target's ID.</param>
        /// <param name="actionName">The action's name.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains true if deleted, false if not found.</returns>
        Task<bool> DeleteByKeyAsync(string userId, string targetId, string actionName);

        /// <summary>
        /// Deletes a range of UserAuthActions from the repository.
        /// Note: EF Core's RemoveRange typically works on tracked entities.
        /// This might require fetching them first or constructing matching instances if deleting by criteria.
        /// For simplicity, this interface assumes the entities are provided.
        /// </summary>
        /// <param name="userAuthActions">The collection of UserAuthActions to delete.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task DeleteRangeAsync(IEnumerable<UserAuthAction> userAuthActions);

        // Consider if an UpdateAsync method is needed. UserAuthAction is effectively a link table entry;
        // updates usually mean deleting an old one and adding a new one if a key part changes.
        // If only non-key properties were to change (none exist here), UpdateAsync would be relevant.
    }
}
